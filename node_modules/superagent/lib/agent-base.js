"use strict";

const defaults = ['use', 'on', 'once', 'set', 'query', 'type', 'accept', 'auth', 'withCredentials', 'sortQuery', 'retry', 'ok', 'redirects', 'timeout', 'buffer', 'serialize', 'parse', 'ca', 'key', 'pfx', 'cert', 'disableTLSCerts'];
class Agent {
  constructor() {
    this._defaults = [];
  }
  _setDefaults(request) {
    for (const def of this._defaults) {
      request[def.fn](...def.args);
    }
  }
}
for (const fn of defaults) {
  // Default setting for all requests from this agent
  Agent.prototype[fn] = function (...args) {
    this._defaults.push({
      fn,
      args
    });
    return this;
  };
}
module.exports = Agent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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