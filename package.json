{"name": "teknikservis", "version": "1.0.0", "description": "Technical Service Management System", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "db:migrate": "node src/config/migrate.js", "db:seed": "node src/config/seed.js"}, "keywords": ["technical-service", "management", "api", "nodejs"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.1", "mysql2": "^3.14.1", "sequelize": "^6.37.7", "sqlite3": "^5.1.7", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"jest": "^30.0.4", "nodemon": "^3.1.10", "supertest": "^7.1.3"}}