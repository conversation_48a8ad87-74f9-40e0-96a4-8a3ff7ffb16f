// Global variables
let currentUser = null;
let authToken = null;
const API_BASE = '/api';

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    checkAuth();
});

// Authentication functions
function checkAuth() {
    const token = localStorage.getItem('authToken');
    const user = localStorage.getItem('currentUser');
    
    if (token && user) {
        authToken = token;
        currentUser = JSON.parse(user);
        setupUI();
        showDashboard();
    } else {
        showLogin();
    }
}

function showLogin() {
    const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
    loginModal.show();
    
    document.getElementById('loginForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        
        try {
            const response = await fetch(`${API_BASE}/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ email, password })
            });
            
            const data = await response.json();
            
            if (response.ok) {
                authToken = data.token;
                currentUser = data.user;
                localStorage.setItem('authToken', authToken);
                localStorage.setItem('currentUser', JSON.stringify(currentUser));
                
                loginModal.hide();
                setupUI();
                showDashboard();
            } else {
                showAlert('Giriş başarısız: ' + data.message, 'danger');
            }
        } catch (error) {
            showAlert('Bağlantı hatası: ' + error.message, 'danger');
        }
    });
}

function logout() {
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentUser');
    authToken = null;
    currentUser = null;
    location.reload();
}

function setupUI() {
    document.getElementById('userName').textContent = currentUser.name;
    
    // Show/hide menu items based on role
    if (currentUser.role === 'admin') {
        document.getElementById('usersMenuItem').style.display = 'block';
    }
    
    // Update active nav link
    updateActiveNavLink('dashboard');
}

// Navigation functions
function updateActiveNavLink(section) {
    document.querySelectorAll('.sidebar .nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    const activeLink = document.querySelector(`[onclick="show${section.charAt(0).toUpperCase() + section.slice(1)}()"]`);
    if (activeLink) {
        activeLink.classList.add('active');
    }
}

// API helper function
async function apiCall(endpoint, options = {}) {
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`
        }
    };
    
    const mergedOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };
    
    try {
        const response = await fetch(`${API_BASE}${endpoint}`, mergedOptions);
        
        if (response.status === 401) {
            logout();
            return;
        }
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || 'API call failed');
        }
        
        return data;
    } catch (error) {
        console.error('API call error:', error);
        throw error;
    }
}

// Utility functions
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Auto dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function formatDate(dateString) {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('tr-TR');
}

function formatDateTime(dateString) {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleString('tr-TR');
}

function formatCurrency(amount) {
    if (!amount) return '0,00 ₺';
    return new Intl.NumberFormat('tr-TR', {
        style: 'currency',
        currency: 'TRY'
    }).format(amount);
}

function getStatusBadge(status) {
    const statusMap = {
        'Pending': { class: 'warning', text: 'Beklemede' },
        'Assigned': { class: 'info', text: 'Atandı' },
        'OnTheWay': { class: 'primary', text: 'Yolda' },
        'InRepair': { class: 'secondary', text: 'Onarımda' },
        'Completed': { class: 'success', text: 'Tamamlandı' }
    };
    
    const statusInfo = statusMap[status] || { class: 'secondary', text: status };
    return `<span class="badge bg-${statusInfo.class} status-badge">${statusInfo.text}</span>`;
}

// Dashboard functions
async function showDashboard() {
    updateActiveNavLink('dashboard');
    
    try {
        // Get dashboard stats
        const [customers, devices, serviceRequests, invoices] = await Promise.all([
            apiCall('/customers?limit=1'),
            apiCall('/devices?limit=1'),
            apiCall('/service-requests?limit=1'),
            apiCall('/invoices?limit=1')
        ]);
        
        const pendingRequests = await apiCall('/service-requests?status=Pending&limit=1');
        const unpaidInvoices = await apiCall('/invoices?paid=false&limit=1');
        
        document.getElementById('mainContent').innerHTML = `
            <div class="row mb-4">
                <div class="col-12">
                    <h2><i class="fas fa-tachometer-alt"></i> Dashboard</h2>
                </div>
            </div>
            
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card card-stats">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">Toplam Müşteri</h5>
                                    <h2>${customers.pagination.totalItems}</h2>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3 mb-3">
                    <div class="card card-stats">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">Toplam Cihaz</h5>
                                    <h2>${devices.pagination.totalItems}</h2>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-laptop fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3 mb-3">
                    <div class="card card-stats">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">Bekleyen Talepler</h5>
                                    <h2>${pendingRequests.pagination.totalItems}</h2>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3 mb-3">
                    <div class="card card-stats">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">Ödenmemiş Fatura</h5>
                                    <h2>${unpaidInvoices.pagination.totalItems}</h2>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-file-invoice fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-clipboard-list"></i> Son Servis Talepleri</h5>
                        </div>
                        <div class="card-body">
                            <div id="recentServiceRequests">Yükleniyor...</div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-bell"></i> Bugünkü Hatırlatıcılar</h5>
                        </div>
                        <div class="card-body">
                            <div id="todayReminders">Yükleniyor...</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Load recent service requests
        loadRecentServiceRequests();
        
        // Load today's reminders
        loadTodayReminders();
        
    } catch (error) {
        showAlert('Dashboard yüklenirken hata oluştu: ' + error.message, 'danger');
    }
}

async function loadRecentServiceRequests() {
    try {
        const data = await apiCall('/service-requests?limit=5');
        const container = document.getElementById('recentServiceRequests');

        if (data.serviceRequests.length === 0) {
            container.innerHTML = '<p class="text-muted">Henüz servis talebi yok.</p>';
            return;
        }

        const html = data.serviceRequests.map(request => `
            <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                <div>
                    <strong>${request.device.customer.name}</strong><br>
                    <small class="text-muted">${request.device.brand} ${request.device.model}</small>
                </div>
                <div class="text-end">
                    ${getStatusBadge(request.status)}<br>
                    <small class="text-muted">${formatDate(request.request_date)}</small>
                </div>
            </div>
        `).join('');

        container.innerHTML = html;
    } catch (error) {
        document.getElementById('recentServiceRequests').innerHTML =
            '<p class="text-danger">Yüklenirken hata oluştu.</p>';
    }
}

async function loadTodayReminders() {
    try {
        const data = await apiCall('/reminders/due');
        const container = document.getElementById('todayReminders');

        if (data.reminders.length === 0) {
            container.innerHTML = '<p class="text-muted">Bugün için hatırlatıcı yok.</p>';
            return;
        }

        const html = data.reminders.map(reminder => `
            <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                <div>
                    <strong>${reminder.title}</strong><br>
                    <small class="text-muted">${reminder.customer.name}</small>
                </div>
                <div class="text-end">
                    <small class="text-muted">${formatDate(reminder.remind_date)}</small>
                </div>
            </div>
        `).join('');

        container.innerHTML = html;
    } catch (error) {
        document.getElementById('todayReminders').innerHTML =
            '<p class="text-danger">Yüklenirken hata oluştu.</p>';
    }
}

// Customer management functions
async function showCustomers() {
    updateActiveNavLink('customers');

    document.getElementById('mainContent').innerHTML = `
        <div class="row mb-4">
            <div class="col-md-6">
                <h2><i class="fas fa-users"></i> Müşteriler</h2>
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-primary" onclick="showCustomerForm()">
                    <i class="fas fa-plus"></i> Yeni Müşteri
                </button>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Müşteri Listesi</h5>
                    </div>
                    <div class="col-md-6">
                        <input type="text" class="form-control" id="customerSearch"
                               placeholder="Müşteri ara..." onkeyup="searchCustomers()">
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Ad Soyad</th>
                                <th>Telefon</th>
                                <th>E-posta</th>
                                <th>Adres</th>
                                <th>Kayıt Tarihi</th>
                                <th>İşlemler</th>
                            </tr>
                        </thead>
                        <tbody id="customersTableBody">
                            <tr><td colspan="6" class="text-center">Yükleniyor...</td></tr>
                        </tbody>
                    </table>
                </div>
                <div id="customersPagination"></div>
            </div>
        </div>
    `;

    loadCustomers();
}

async function loadCustomers(page = 1, search = '') {
    try {
        const data = await apiCall(`/customers?page=${page}&limit=10&search=${search}`);
        const tbody = document.getElementById('customersTableBody');

        if (data.customers.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center">Müşteri bulunamadı.</td></tr>';
            return;
        }

        const html = data.customers.map(customer => `
            <tr>
                <td><strong>${customer.name}</strong></td>
                <td>${customer.phone}</td>
                <td>${customer.email || '-'}</td>
                <td>${customer.address || '-'}</td>
                <td>${formatDate(customer.created_at)}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="editCustomer(${customer.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteCustomer(${customer.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');

        tbody.innerHTML = html;

        // Update pagination
        updatePagination('customersPagination', data.pagination, (page) => loadCustomers(page, search));

    } catch (error) {
        document.getElementById('customersTableBody').innerHTML =
            '<tr><td colspan="6" class="text-center text-danger">Yüklenirken hata oluştu.</td></tr>';
        showAlert('Müşteriler yüklenirken hata oluştu: ' + error.message, 'danger');
    }
}

function searchCustomers() {
    const search = document.getElementById('customerSearch').value;
    loadCustomers(1, search);
}

function showCustomerForm(customerId = null) {
    const isEdit = customerId !== null;
    const title = isEdit ? 'Müşteri Düzenle' : 'Yeni Müşteri';

    document.getElementById('genericModalTitle').textContent = title;
    document.getElementById('genericModalBody').innerHTML = `
        <form id="customerForm">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="customerName" class="form-label">Ad Soyad *</label>
                        <input type="text" class="form-control" id="customerName" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="customerPhone" class="form-label">Telefon *</label>
                        <input type="tel" class="form-control" id="customerPhone" required>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="customerEmail" class="form-label">E-posta</label>
                        <input type="email" class="form-control" id="customerEmail">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="customerAddress" class="form-label">Adres</label>
                        <textarea class="form-control" id="customerAddress" rows="3"></textarea>
                    </div>
                </div>
            </div>
            <div class="text-end">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                <button type="submit" class="btn btn-primary">${isEdit ? 'Güncelle' : 'Kaydet'}</button>
            </div>
        </form>
    `;

    const modal = new bootstrap.Modal(document.getElementById('genericModal'));
    modal.show();

    // Load customer data if editing
    if (isEdit) {
        loadCustomerData(customerId);
    }

    // Handle form submission
    document.getElementById('customerForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        await saveCustomer(customerId, modal);
    });
}

async function loadCustomerData(customerId) {
    try {
        const data = await apiCall(`/customers/${customerId}`);
        const customer = data.customer;

        document.getElementById('customerName').value = customer.name;
        document.getElementById('customerPhone').value = customer.phone;
        document.getElementById('customerEmail').value = customer.email || '';
        document.getElementById('customerAddress').value = customer.address || '';
    } catch (error) {
        showAlert('Müşteri bilgileri yüklenirken hata oluştu: ' + error.message, 'danger');
    }
}

async function saveCustomer(customerId, modal) {
    try {
        const customerData = {
            name: document.getElementById('customerName').value,
            phone: document.getElementById('customerPhone').value,
            email: document.getElementById('customerEmail').value || null,
            address: document.getElementById('customerAddress').value || null
        };

        const endpoint = customerId ? `/customers/${customerId}` : '/customers';
        const method = customerId ? 'PUT' : 'POST';

        await apiCall(endpoint, {
            method,
            body: JSON.stringify(customerData)
        });

        modal.hide();
        showAlert(customerId ? 'Müşteri güncellendi.' : 'Müşteri eklendi.', 'success');
        loadCustomers();
    } catch (error) {
        showAlert('Kaydetme hatası: ' + error.message, 'danger');
    }
}

function editCustomer(customerId) {
    showCustomerForm(customerId);
}

async function deleteCustomer(customerId) {
    if (!confirm('Bu müşteriyi silmek istediğinizden emin misiniz?')) {
        return;
    }

    try {
        await apiCall(`/customers/${customerId}`, { method: 'DELETE' });
        showAlert('Müşteri silindi.', 'success');
        loadCustomers();
    } catch (error) {
        showAlert('Silme hatası: ' + error.message, 'danger');
    }
}

// Service Requests management
async function showServiceRequests() {
    updateActiveNavLink('serviceRequests');

    document.getElementById('mainContent').innerHTML = `
        <div class="row mb-4">
            <div class="col-md-6">
                <h2><i class="fas fa-clipboard-list"></i> Servis Talepleri</h2>
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-primary" onclick="showServiceRequestForm()">
                    <i class="fas fa-plus"></i> Yeni Talep
                </button>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <div class="row">
                    <div class="col-md-4">
                        <h5>Servis Talepleri</h5>
                    </div>
                    <div class="col-md-4">
                        <select class="form-select" id="statusFilter" onchange="filterServiceRequests()">
                            <option value="">Tüm Durumlar</option>
                            <option value="Pending">Beklemede</option>
                            <option value="Assigned">Atandı</option>
                            <option value="OnTheWay">Yolda</option>
                            <option value="InRepair">Onarımda</option>
                            <option value="Completed">Tamamlandı</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" id="serviceRequestSearch"
                               placeholder="Müşteri ara..." onkeyup="searchServiceRequests()">
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Müşteri</th>
                                <th>Cihaz</th>
                                <th>Durum</th>
                                <th>Atanan</th>
                                <th>Talep Tarihi</th>
                                <th>Tahmini Bitiş</th>
                                <th>İşlemler</th>
                            </tr>
                        </thead>
                        <tbody id="serviceRequestsTableBody">
                            <tr><td colspan="7" class="text-center">Yükleniyor...</td></tr>
                        </tbody>
                    </table>
                </div>
                <div id="serviceRequestsPagination"></div>
            </div>
        </div>
    `;

    loadServiceRequests();
}

async function loadServiceRequests(page = 1, status = '', search = '') {
    try {
        let url = `/service-requests?page=${page}&limit=10`;
        if (status) url += `&status=${status}`;
        if (search) url += `&search=${search}`;

        const data = await apiCall(url);
        const tbody = document.getElementById('serviceRequestsTableBody');

        if (data.serviceRequests.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center">Servis talebi bulunamadı.</td></tr>';
            return;
        }

        const html = data.serviceRequests.map(request => `
            <tr>
                <td><strong>${request.device.customer.name}</strong></td>
                <td>${request.device.brand} ${request.device.model}</td>
                <td>${getStatusBadge(request.status)}</td>
                <td>${request.assigned_to || '-'}</td>
                <td>${formatDate(request.request_date)}</td>
                <td>${formatDate(request.estimated_completion)}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewServiceRequest(${request.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="editServiceRequest(${request.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                </td>
            </tr>
        `).join('');

        tbody.innerHTML = html;

        // Update pagination
        updatePagination('serviceRequestsPagination', data.pagination, (page) => loadServiceRequests(page, status, search));

    } catch (error) {
        document.getElementById('serviceRequestsTableBody').innerHTML =
            '<tr><td colspan="7" class="text-center text-danger">Yüklenirken hata oluştu.</td></tr>';
        showAlert('Servis talepleri yüklenirken hata oluştu: ' + error.message, 'danger');
    }
}

function filterServiceRequests() {
    const status = document.getElementById('statusFilter').value;
    const search = document.getElementById('serviceRequestSearch').value;
    loadServiceRequests(1, status, search);
}

function searchServiceRequests() {
    const status = document.getElementById('statusFilter').value;
    const search = document.getElementById('serviceRequestSearch').value;
    loadServiceRequests(1, status, search);
}

// Utility function for pagination
function updatePagination(containerId, pagination, loadFunction) {
    const container = document.getElementById(containerId);

    if (pagination.totalPages <= 1) {
        container.innerHTML = '';
        return;
    }

    let html = '<nav><ul class="pagination justify-content-center">';

    // Previous button
    if (pagination.currentPage > 1) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadCustomers(${pagination.currentPage - 1})">Önceki</a></li>`;
    }

    // Page numbers
    for (let i = Math.max(1, pagination.currentPage - 2); i <= Math.min(pagination.totalPages, pagination.currentPage + 2); i++) {
        const active = i === pagination.currentPage ? 'active' : '';
        html += `<li class="page-item ${active}"><a class="page-link" href="#" onclick="loadCustomers(${i})">${i}</a></li>`;
    }

    // Next button
    if (pagination.currentPage < pagination.totalPages) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadCustomers(${pagination.currentPage + 1})">Sonraki</a></li>`;
    }

    html += '</ul></nav>';
    container.innerHTML = html;
}

// Placeholder functions for other sections
function showDevices() {
    updateActiveNavLink('devices');
    document.getElementById('mainContent').innerHTML = '<h2>Cihazlar - Geliştiriliyor...</h2>';
}

function showInvoices() {
    updateActiveNavLink('invoices');
    document.getElementById('mainContent').innerHTML = '<h2>Faturalar - Geliştiriliyor...</h2>';
}

function showReminders() {
    updateActiveNavLink('reminders');
    document.getElementById('mainContent').innerHTML = '<h2>Hatırlatıcılar - Geliştiriliyor...</h2>';
}

function showUsers() {
    updateActiveNavLink('users');
    document.getElementById('mainContent').innerHTML = '<h2>Kullanıcılar - Geliştiriliyor...</h2>';
}

function showProfile() {
    document.getElementById('mainContent').innerHTML = '<h2>Profil - Geliştiriliyor...</h2>';
}
