const jwt = require('jsonwebtoken');
const User = require('../models/User');

// Middleware to verify JWT token
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({ message: 'Access token required' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Get user from database to ensure they still exist and get current role
    const user = await User.findByPk(decoded.userId);
    if (!user) {
      return res.status(401).json({ message: 'Invalid token - user not found' });
    }

    req.user = {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role
    };
    
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ message: 'Invalid token' });
    }
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ message: 'Token expired' });
    }
    return res.status(500).json({ message: 'Token verification failed' });
  }
};

// Middleware to check user roles
const authorizeRoles = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ 
        message: `Access denied. Required roles: ${roles.join(', ')}. Your role: ${req.user.role}` 
      });
    }

    next();
  };
};

// Specific role middleware functions
const requireAdmin = authorizeRoles('admin');
const requireTechnician = authorizeRoles('admin', 'technician');
const requireCustomerService = authorizeRoles('admin', 'customer_service');
const requireAnyRole = authorizeRoles('admin', 'technician', 'customer_service');

// Middleware to check if user can access specific service request
const canAccessServiceRequest = async (req, res, next) => {
  try {
    const { ServiceRequest, Device } = require('../models');
    const serviceRequestId = req.params.id || req.body.service_request_id;
    
    if (!serviceRequestId) {
      return res.status(400).json({ message: 'Service request ID required' });
    }

    const serviceRequest = await ServiceRequest.findByPk(serviceRequestId, {
      include: [{
        model: Device,
        as: 'device'
      }]
    });

    if (!serviceRequest) {
      return res.status(404).json({ message: 'Service request not found' });
    }

    // Admin can access all
    if (req.user.role === 'admin') {
      req.serviceRequest = serviceRequest;
      return next();
    }

    // Technician can only access assigned requests
    if (req.user.role === 'technician') {
      if (serviceRequest.assigned_to === req.user.name || serviceRequest.assigned_to === req.user.id.toString()) {
        req.serviceRequest = serviceRequest;
        return next();
      }
      return res.status(403).json({ message: 'Access denied - not assigned to this service request' });
    }

    // Customer service can access all
    if (req.user.role === 'customer_service') {
      req.serviceRequest = serviceRequest;
      return next();
    }

    return res.status(403).json({ message: 'Access denied' });
  } catch (error) {
    return res.status(500).json({ message: 'Error checking service request access' });
  }
};

module.exports = {
  authenticateToken,
  authorizeRoles,
  requireAdmin,
  requireTechnician,
  requireCustomerService,
  requireAnyRole,
  canAccessServiceRequest
};
