const { validationResult } = require('express-validator');
const { <PERSON><PERSON>, Customer, ServiceRequest } = require('../models');
const { Op } = require('sequelize');

// Get all devices with pagination and search
const getAllDevices = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const search = req.query.search || '';
    const customerId = req.query.customer_id;

    let whereClause = {};

    if (search) {
      whereClause[Op.or] = [
        { brand: { [Op.like]: `%${search}%` } },
        { model: { [Op.like]: `%${search}%` } },
        { serial_number: { [Op.like]: `%${search}%` } }
      ];
    }

    if (customerId) {
      whereClause.customer_id = customerId;
    }

    const { count, rows: devices } = await Device.findAndCountAll({
      where: whereClause,
      limit,
      offset,
      order: [['created_at', 'DESC']],
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'name', 'phone', 'email']
        },
        {
          model: ServiceRequest,
          as: 'serviceRequests',
          attributes: ['id', 'status', 'description', 'request_date'],
          limit: 5,
          order: [['created_at', 'DESC']]
        }
      ]
    });

    res.json({
      devices,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(count / limit),
        totalItems: count,
        itemsPerPage: limit
      }
    });
  } catch (error) {
    console.error('Get devices error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get device by ID
const getDeviceById = async (req, res) => {
  try {
    const { id } = req.params;

    const device = await Device.findByPk(id, {
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'name', 'phone', 'email', 'address']
        },
        {
          model: ServiceRequest,
          as: 'serviceRequests',
          order: [['created_at', 'DESC']]
        }
      ]
    });

    if (!device) {
      return res.status(404).json({ message: 'Device not found' });
    }

    res.json({ device });
  } catch (error) {
    console.error('Get device error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Create new device
const createDevice = async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { customer_id, brand, model, serial_number, warranty_expiry } = req.body;

    // Check if customer exists
    const customer = await Customer.findByPk(customer_id);
    if (!customer) {
      return res.status(404).json({ message: 'Customer not found' });
    }

    const device = await Device.create({
      customer_id,
      brand,
      model,
      serial_number,
      warranty_expiry
    });

    // Fetch the created device with customer info
    const createdDevice = await Device.findByPk(device.id, {
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'name', 'phone', 'email']
        }
      ]
    });

    res.status(201).json({
      message: 'Device created successfully',
      device: createdDevice
    });
  } catch (error) {
    console.error('Create device error:', error);

    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        message: 'Validation failed',
        errors: error.errors.map(err => ({
          field: err.path,
          message: err.message
        }))
      });
    }

    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(409).json({
        message: 'Serial number already exists'
      });
    }

    res.status(500).json({ message: 'Internal server error' });
  }
};

// Update device
const updateDevice = async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { customer_id, brand, model, serial_number, warranty_expiry } = req.body;

    const device = await Device.findByPk(id);
    if (!device) {
      return res.status(404).json({ message: 'Device not found' });
    }

    // Check if customer exists (if customer_id is being updated)
    if (customer_id && customer_id !== device.customer_id) {
      const customer = await Customer.findByPk(customer_id);
      if (!customer) {
        return res.status(404).json({ message: 'Customer not found' });
      }
    }

    await device.update({
      customer_id,
      brand,
      model,
      serial_number,
      warranty_expiry
    });

    // Fetch updated device with customer info
    const updatedDevice = await Device.findByPk(id, {
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'name', 'phone', 'email']
        }
      ]
    });

    res.json({
      message: 'Device updated successfully',
      device: updatedDevice
    });
  } catch (error) {
    console.error('Update device error:', error);

    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        message: 'Validation failed',
        errors: error.errors.map(err => ({
          field: err.path,
          message: err.message
        }))
      });
    }

    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(409).json({
        message: 'Serial number already exists'
      });
    }

    res.status(500).json({ message: 'Internal server error' });
  }
};

// Delete device (soft delete)
const deleteDevice = async (req, res) => {
  try {
    const { id } = req.params;

    const device = await Device.findByPk(id);
    if (!device) {
      return res.status(404).json({ message: 'Device not found' });
    }

    await device.destroy();

    res.json({ message: 'Device deleted successfully' });
  } catch (error) {
    console.error('Delete device error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get devices by customer ID
const getDevicesByCustomer = async (req, res) => {
  try {
    const { customerId } = req.params;

    const customer = await Customer.findByPk(customerId);
    if (!customer) {
      return res.status(404).json({ message: 'Customer not found' });
    }

    const devices = await Device.findAll({
      where: { customer_id: customerId },
      order: [['created_at', 'DESC']],
      include: [
        {
          model: ServiceRequest,
          as: 'serviceRequests',
          attributes: ['id', 'status', 'description', 'request_date'],
          limit: 3,
          order: [['created_at', 'DESC']]
        }
      ]
    });

    res.json({ devices });
  } catch (error) {
    console.error('Get devices by customer error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

module.exports = {
  getAllDevices,
  getDeviceById,
  createDevice,
  updateDevice,
  deleteDevice,
  getDevicesByCustomer
};
