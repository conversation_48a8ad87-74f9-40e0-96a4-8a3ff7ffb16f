const { validationResult } = require('express-validator');
const { <PERSON>minder, Customer } = require('../models');
const { Op } = require('sequelize');

// Get all reminders with pagination and filtering
const getAllReminders = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const is_sent = req.query.is_sent;
    const date_from = req.query.date_from;
    const date_to = req.query.date_to;
    const customer_id = req.query.customer_id;

    let whereClause = {};

    if (is_sent !== undefined) {
      whereClause.is_sent = is_sent === 'true';
    }

    if (customer_id) {
      whereClause.customer_id = customer_id;
    }

    if (date_from || date_to) {
      whereClause.remind_date = {};
      if (date_from) {
        whereClause.remind_date[Op.gte] = date_from;
      }
      if (date_to) {
        whereClause.remind_date[Op.lte] = date_to;
      }
    }

    const { count, rows: reminders } = await Reminder.findAndCountAll({
      where: whereClause,
      limit,
      offset,
      order: [['remind_date', 'ASC']],
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'name', 'phone', 'email']
        }
      ]
    });

    res.json({
      reminders,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(count / limit),
        totalItems: count,
        itemsPerPage: limit
      }
    });
  } catch (error) {
    console.error('Get reminders error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get reminder by ID
const getReminderById = async (req, res) => {
  try {
    const { id } = req.params;

    const reminder = await Reminder.findByPk(id, {
      include: [
        {
          model: Customer,
          as: 'customer'
        }
      ]
    });

    if (!reminder) {
      return res.status(404).json({ message: 'Reminder not found' });
    }

    res.json({ reminder });
  } catch (error) {
    console.error('Get reminder error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Create new reminder
const createReminder = async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { customer_id, title, note, remind_date } = req.body;

    // Check if customer exists
    const customer = await Customer.findByPk(customer_id);
    if (!customer) {
      return res.status(404).json({ message: 'Customer not found' });
    }

    const reminder = await Reminder.create({
      customer_id,
      title,
      note,
      remind_date,
      is_sent: false
    });

    // Fetch the created reminder with customer info
    const createdReminder = await Reminder.findByPk(reminder.id, {
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'name', 'phone', 'email']
        }
      ]
    });

    res.status(201).json({
      message: 'Reminder created successfully',
      reminder: createdReminder
    });
  } catch (error) {
    console.error('Create reminder error:', error);

    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        message: 'Validation failed',
        errors: error.errors.map(err => ({
          field: err.path,
          message: err.message
        }))
      });
    }

    res.status(500).json({ message: 'Internal server error' });
  }
};

// Update reminder
const updateReminder = async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { customer_id, title, note, remind_date, is_sent } = req.body;

    const reminder = await Reminder.findByPk(id);
    if (!reminder) {
      return res.status(404).json({ message: 'Reminder not found' });
    }

    // Check if customer exists (if customer_id is being updated)
    if (customer_id && customer_id !== reminder.customer_id) {
      const customer = await Customer.findByPk(customer_id);
      if (!customer) {
        return res.status(404).json({ message: 'Customer not found' });
      }
    }

    await reminder.update({
      customer_id,
      title,
      note,
      remind_date,
      is_sent
    });

    // Fetch updated reminder with customer info
    const updatedReminder = await Reminder.findByPk(id, {
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'name', 'phone', 'email']
        }
      ]
    });

    res.json({
      message: 'Reminder updated successfully',
      reminder: updatedReminder
    });
  } catch (error) {
    console.error('Update reminder error:', error);

    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        message: 'Validation failed',
        errors: error.errors.map(err => ({
          field: err.path,
          message: err.message
        }))
      });
    }

    res.status(500).json({ message: 'Internal server error' });
  }
};

// Delete reminder (soft delete)
const deleteReminder = async (req, res) => {
  try {
    const { id } = req.params;

    const reminder = await Reminder.findByPk(id);
    if (!reminder) {
      return res.status(404).json({ message: 'Reminder not found' });
    }

    await reminder.destroy();

    res.json({ message: 'Reminder deleted successfully' });
  } catch (error) {
    console.error('Delete reminder error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Mark reminder as sent
const markAsSent = async (req, res) => {
  try {
    const { id } = req.params;

    const reminder = await Reminder.findByPk(id);
    if (!reminder) {
      return res.status(404).json({ message: 'Reminder not found' });
    }

    await reminder.update({ is_sent: true });

    res.json({
      message: 'Reminder marked as sent successfully',
      reminder
    });
  } catch (error) {
    console.error('Mark as sent error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get due reminders (reminders that should be sent today or are overdue)
const getDueReminders = async (req, res) => {
  try {
    const today = new Date().toISOString().split('T')[0];

    const dueReminders = await Reminder.findAll({
      where: {
        remind_date: { [Op.lte]: today },
        is_sent: false
      },
      order: [['remind_date', 'ASC']],
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'name', 'phone', 'email']
        }
      ]
    });

    res.json({ reminders: dueReminders });
  } catch (error) {
    console.error('Get due reminders error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

module.exports = {
  getAllReminders,
  getReminderById,
  createReminder,
  updateReminder,
  deleteReminder,
  markAsSent,
  getDueReminders
};
