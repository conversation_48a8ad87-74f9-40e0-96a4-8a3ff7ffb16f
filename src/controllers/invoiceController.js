const { validationResult } = require('express-validator');
const { Invoice, ServiceRequest, Device, Customer } = require('../models');
const { deleteFile, getFileUrl } = require('../middleware/upload');
const { Op } = require('sequelize');
const path = require('path');

// Get all invoices with pagination and filtering
const getAllInvoices = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const paid = req.query.paid;
    const search = req.query.search || '';

    let whereClause = {};

    if (paid !== undefined) {
      whereClause.paid = paid === 'true';
    }

    const includeClause = [
      {
        model: ServiceRequest,
        as: 'serviceRequest',
        include: [
          {
            model: Device,
            as: 'device',
            include: [
              {
                model: Customer,
                as: 'customer',
                attributes: ['id', 'name', 'phone', 'email']
              }
            ]
          }
        ]
      }
    ];

    // Add search functionality
    if (search) {
      includeClause[0].include[0].include[0].where = {
        [Op.or]: [
          { name: { [Op.like]: `%${search}%` } },
          { phone: { [Op.like]: `%${search}%` } }
        ]
      };
    }

    const { count, rows: invoices } = await Invoice.findAndCountAll({
      where: whereClause,
      limit,
      offset,
      order: [['created_at', 'DESC']],
      include: includeClause
    });

    // Add PDF URLs to invoices
    const invoicesWithUrls = invoices.map(invoice => {
      const invoiceData = invoice.toJSON();
      if (invoiceData.invoice_pdf) {
        invoiceData.pdfUrl = getFileUrl(req, invoiceData.invoice_pdf, 'pdfs');
      }
      return invoiceData;
    });

    res.json({
      invoices: invoicesWithUrls,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(count / limit),
        totalItems: count,
        itemsPerPage: limit
      }
    });
  } catch (error) {
    console.error('Get invoices error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get invoice by ID
const getInvoiceById = async (req, res) => {
  try {
    const { id } = req.params;

    const invoice = await Invoice.findByPk(id, {
      include: [
        {
          model: ServiceRequest,
          as: 'serviceRequest',
          include: [
            {
              model: Device,
              as: 'device',
              include: [
                {
                  model: Customer,
                  as: 'customer'
                }
              ]
            }
          ]
        }
      ]
    });

    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    const invoiceData = invoice.toJSON();
    if (invoiceData.invoice_pdf) {
      invoiceData.pdfUrl = getFileUrl(req, invoiceData.invoice_pdf, 'pdfs');
    }

    res.json({ invoice: invoiceData });
  } catch (error) {
    console.error('Get invoice error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Create new invoice
const createInvoice = async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      // Delete uploaded file if validation fails
      if (req.uploadedPDF) {
        deleteFile(req.uploadedPDF.path);
      }
      return res.status(400).json({
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { service_request_id, amount, due_date } = req.body;

    // Check if service request exists
    const serviceRequest = await ServiceRequest.findByPk(service_request_id, {
      include: [
        {
          model: Device,
          as: 'device',
          include: [
            {
              model: Customer,
              as: 'customer'
            }
          ]
        }
      ]
    });

    if (!serviceRequest) {
      // Delete uploaded file if service request not found
      if (req.uploadedPDF) {
        deleteFile(req.uploadedPDF.path);
      }
      return res.status(404).json({ message: 'Service request not found' });
    }

    const invoiceData = {
      service_request_id,
      amount: parseFloat(amount),
      due_date,
      paid: false
    };

    // Add PDF filename if uploaded
    if (req.uploadedPDF) {
      invoiceData.invoice_pdf = req.uploadedPDF.filename;
    }

    const invoice = await Invoice.create(invoiceData);

    // Fetch the created invoice with related data
    const createdInvoice = await Invoice.findByPk(invoice.id, {
      include: [
        {
          model: ServiceRequest,
          as: 'serviceRequest',
          include: [
            {
              model: Device,
              as: 'device',
              include: [
                {
                  model: Customer,
                  as: 'customer'
                }
              ]
            }
          ]
        }
      ]
    });

    const responseData = createdInvoice.toJSON();
    if (responseData.invoice_pdf) {
      responseData.pdfUrl = getFileUrl(req, responseData.invoice_pdf, 'pdfs');
    }

    res.status(201).json({
      message: 'Invoice created successfully',
      invoice: responseData
    });
  } catch (error) {
    console.error('Create invoice error:', error);

    // Delete uploaded file if error occurs
    if (req.uploadedPDF) {
      deleteFile(req.uploadedPDF.path);
    }

    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        message: 'Validation failed',
        errors: error.errors.map(err => ({
          field: err.path,
          message: err.message
        }))
      });
    }

    res.status(500).json({ message: 'Internal server error' });
  }
};

// Update invoice
const updateInvoice = async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      // Delete uploaded file if validation fails
      if (req.uploadedPDF) {
        deleteFile(req.uploadedPDF.path);
      }
      return res.status(400).json({
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { amount, paid, due_date } = req.body;

    const invoice = await Invoice.findByPk(id);
    if (!invoice) {
      // Delete uploaded file if invoice not found
      if (req.uploadedPDF) {
        deleteFile(req.uploadedPDF.path);
      }
      return res.status(404).json({ message: 'Invoice not found' });
    }

    const updateData = {};
    if (amount !== undefined) updateData.amount = parseFloat(amount);
    if (paid !== undefined) updateData.paid = paid;
    if (due_date !== undefined) updateData.due_date = due_date;

    // Handle PDF update
    if (req.uploadedPDF) {
      // Delete old PDF if exists
      if (invoice.invoice_pdf) {
        const oldPdfPath = path.join(__dirname, '../uploads/pdfs', invoice.invoice_pdf);
        deleteFile(oldPdfPath);
      }
      updateData.invoice_pdf = req.uploadedPDF.filename;
    }

    await invoice.update(updateData);

    // Fetch updated invoice with related data
    const updatedInvoice = await Invoice.findByPk(id, {
      include: [
        {
          model: ServiceRequest,
          as: 'serviceRequest',
          include: [
            {
              model: Device,
              as: 'device',
              include: [
                {
                  model: Customer,
                  as: 'customer'
                }
              ]
            }
          ]
        }
      ]
    });

    const responseData = updatedInvoice.toJSON();
    if (responseData.invoice_pdf) {
      responseData.pdfUrl = getFileUrl(req, responseData.invoice_pdf, 'pdfs');
    }

    res.json({
      message: 'Invoice updated successfully',
      invoice: responseData
    });
  } catch (error) {
    console.error('Update invoice error:', error);

    // Delete uploaded file if error occurs
    if (req.uploadedPDF) {
      deleteFile(req.uploadedPDF.path);
    }

    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        message: 'Validation failed',
        errors: error.errors.map(err => ({
          field: err.path,
          message: err.message
        }))
      });
    }

    res.status(500).json({ message: 'Internal server error' });
  }
};

// Delete invoice (soft delete)
const deleteInvoice = async (req, res) => {
  try {
    const { id } = req.params;

    const invoice = await Invoice.findByPk(id);
    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    // Delete associated PDF file
    if (invoice.invoice_pdf) {
      const pdfPath = path.join(__dirname, '../uploads/pdfs', invoice.invoice_pdf);
      deleteFile(pdfPath);
    }

    await invoice.destroy();

    res.json({ message: 'Invoice deleted successfully' });
  } catch (error) {
    console.error('Delete invoice error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Mark invoice as paid
const markAsPaid = async (req, res) => {
  try {
    const { id } = req.params;

    const invoice = await Invoice.findByPk(id);
    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    await invoice.update({ paid: true });

    res.json({
      message: 'Invoice marked as paid successfully',
      invoice
    });
  } catch (error) {
    console.error('Mark as paid error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

module.exports = {
  getAllInvoices,
  getInvoiceById,
  createInvoice,
  updateInvoice,
  deleteInvoice,
  markAsPaid
};
