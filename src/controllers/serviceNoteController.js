const { validationResult } = require('express-validator');
const { ServiceNote, ServiceRequest, Device, Customer } = require('../models');
const { deleteFile, getFileUrl } = require('../middleware/upload');
const path = require('path');

// Get all service notes for a service request
const getServiceNotesByRequest = async (req, res) => {
  try {
    const { serviceRequestId } = req.params;

    // Check if service request exists and user has access
    const serviceRequest = await ServiceRequest.findByPk(serviceRequestId, {
      include: [
        {
          model: Device,
          as: 'device',
          include: [
            {
              model: Customer,
              as: 'customer'
            }
          ]
        }
      ]
    });

    if (!serviceRequest) {
      return res.status(404).json({ message: 'Service request not found' });
    }

    // Check access permissions for technicians
    if (req.user.role === 'technician' && 
        serviceRequest.assigned_to !== req.user.name && 
        serviceRequest.assigned_to !== req.user.id.toString()) {
      return res.status(403).json({ message: 'Access denied - not assigned to this service request' });
    }

    const serviceNotes = await ServiceNote.findAll({
      where: { service_request_id: serviceRequestId },
      order: [['created_at', 'DESC']]
    });

    // Add photo URLs to service notes
    const notesWithUrls = serviceNotes.map(note => {
      const noteData = note.toJSON();
      if (noteData.photo) {
        noteData.photoUrl = getFileUrl(req, noteData.photo, 'photos');
      }
      return noteData;
    });

    res.json({ serviceNotes: notesWithUrls });
  } catch (error) {
    console.error('Get service notes error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get service note by ID
const getServiceNoteById = async (req, res) => {
  try {
    const { id } = req.params;

    const serviceNote = await ServiceNote.findByPk(id, {
      include: [
        {
          model: ServiceRequest,
          as: 'serviceRequest',
          include: [
            {
              model: Device,
              as: 'device',
              include: [
                {
                  model: Customer,
                  as: 'customer'
                }
              ]
            }
          ]
        }
      ]
    });

    if (!serviceNote) {
      return res.status(404).json({ message: 'Service note not found' });
    }

    // Check access permissions for technicians
    if (req.user.role === 'technician' && 
        serviceNote.serviceRequest.assigned_to !== req.user.name && 
        serviceNote.serviceRequest.assigned_to !== req.user.id.toString()) {
      return res.status(403).json({ message: 'Access denied - not assigned to this service request' });
    }

    const noteData = serviceNote.toJSON();
    if (noteData.photo) {
      noteData.photoUrl = getFileUrl(req, noteData.photo, 'photos');
    }

    res.json({ serviceNote: noteData });
  } catch (error) {
    console.error('Get service note error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Create new service note
const createServiceNote = async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      // Delete uploaded file if validation fails
      if (req.uploadedPhoto) {
        deleteFile(req.uploadedPhoto.path);
      }
      return res.status(400).json({
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { service_request_id, note } = req.body;
    const created_by = req.user.name || req.user.email;

    // Check if service request exists and user has access
    const serviceRequest = await ServiceRequest.findByPk(service_request_id);
    if (!serviceRequest) {
      // Delete uploaded file if service request not found
      if (req.uploadedPhoto) {
        deleteFile(req.uploadedPhoto.path);
      }
      return res.status(404).json({ message: 'Service request not found' });
    }

    // Check access permissions for technicians
    if (req.user.role === 'technician' && 
        serviceRequest.assigned_to !== req.user.name && 
        serviceRequest.assigned_to !== req.user.id.toString()) {
      // Delete uploaded file if access denied
      if (req.uploadedPhoto) {
        deleteFile(req.uploadedPhoto.path);
      }
      return res.status(403).json({ message: 'Access denied - not assigned to this service request' });
    }

    const serviceNoteData = {
      service_request_id,
      note,
      created_by
    };

    // Add photo filename if uploaded
    if (req.uploadedPhoto) {
      serviceNoteData.photo = req.uploadedPhoto.filename;
    }

    const serviceNote = await ServiceNote.create(serviceNoteData);

    const responseData = serviceNote.toJSON();
    if (responseData.photo) {
      responseData.photoUrl = getFileUrl(req, responseData.photo, 'photos');
    }

    res.status(201).json({
      message: 'Service note created successfully',
      serviceNote: responseData
    });
  } catch (error) {
    console.error('Create service note error:', error);

    // Delete uploaded file if error occurs
    if (req.uploadedPhoto) {
      deleteFile(req.uploadedPhoto.path);
    }

    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        message: 'Validation failed',
        errors: error.errors.map(err => ({
          field: err.path,
          message: err.message
        }))
      });
    }

    res.status(500).json({ message: 'Internal server error' });
  }
};

// Update service note
const updateServiceNote = async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      // Delete uploaded file if validation fails
      if (req.uploadedPhoto) {
        deleteFile(req.uploadedPhoto.path);
      }
      return res.status(400).json({
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { note } = req.body;

    const serviceNote = await ServiceNote.findByPk(id, {
      include: [
        {
          model: ServiceRequest,
          as: 'serviceRequest'
        }
      ]
    });

    if (!serviceNote) {
      // Delete uploaded file if service note not found
      if (req.uploadedPhoto) {
        deleteFile(req.uploadedPhoto.path);
      }
      return res.status(404).json({ message: 'Service note not found' });
    }

    // Check access permissions for technicians
    if (req.user.role === 'technician' && 
        serviceNote.serviceRequest.assigned_to !== req.user.name && 
        serviceNote.serviceRequest.assigned_to !== req.user.id.toString()) {
      // Delete uploaded file if access denied
      if (req.uploadedPhoto) {
        deleteFile(req.uploadedPhoto.path);
      }
      return res.status(403).json({ message: 'Access denied - not assigned to this service request' });
    }

    const updateData = { note };

    // Handle photo update
    if (req.uploadedPhoto) {
      // Delete old photo if exists
      if (serviceNote.photo) {
        const oldPhotoPath = path.join(__dirname, '../uploads/photos', serviceNote.photo);
        deleteFile(oldPhotoPath);
      }
      updateData.photo = req.uploadedPhoto.filename;
    }

    await serviceNote.update(updateData);

    const responseData = serviceNote.toJSON();
    if (responseData.photo) {
      responseData.photoUrl = getFileUrl(req, responseData.photo, 'photos');
    }

    res.json({
      message: 'Service note updated successfully',
      serviceNote: responseData
    });
  } catch (error) {
    console.error('Update service note error:', error);

    // Delete uploaded file if error occurs
    if (req.uploadedPhoto) {
      deleteFile(req.uploadedPhoto.path);
    }

    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        message: 'Validation failed',
        errors: error.errors.map(err => ({
          field: err.path,
          message: err.message
        }))
      });
    }

    res.status(500).json({ message: 'Internal server error' });
  }
};

// Delete service note (soft delete)
const deleteServiceNote = async (req, res) => {
  try {
    const { id } = req.params;

    const serviceNote = await ServiceNote.findByPk(id, {
      include: [
        {
          model: ServiceRequest,
          as: 'serviceRequest'
        }
      ]
    });

    if (!serviceNote) {
      return res.status(404).json({ message: 'Service note not found' });
    }

    // Check access permissions for technicians
    if (req.user.role === 'technician' && 
        serviceNote.serviceRequest.assigned_to !== req.user.name && 
        serviceNote.serviceRequest.assigned_to !== req.user.id.toString()) {
      return res.status(403).json({ message: 'Access denied - not assigned to this service request' });
    }

    // Delete associated photo file
    if (serviceNote.photo) {
      const photoPath = path.join(__dirname, '../uploads/photos', serviceNote.photo);
      deleteFile(photoPath);
    }

    await serviceNote.destroy();

    res.json({ message: 'Service note deleted successfully' });
  } catch (error) {
    console.error('Delete service note error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

module.exports = {
  getServiceNotesByRequest,
  getServiceNoteById,
  createServiceNote,
  updateServiceNote,
  deleteServiceNote
};
