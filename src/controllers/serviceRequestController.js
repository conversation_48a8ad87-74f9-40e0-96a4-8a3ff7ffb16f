const { validationResult } = require('express-validator');
const { ServiceRequest, <PERSON><PERSON>, Customer, ServiceNote, Invoice } = require('../models');
const { Op } = require('sequelize');

// Get all service requests with pagination and filtering
const getAllServiceRequests = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const status = req.query.status;
    const assigned_to = req.query.assigned_to;
    const search = req.query.search || '';

    let whereClause = {};

    if (status) {
      whereClause.status = status;
    }

    if (assigned_to) {
      whereClause.assigned_to = assigned_to;
    }

    // Role-based filtering
    if (req.user.role === 'technician') {
      whereClause.assigned_to = req.user.name;
    }

    const includeClause = [
      {
        model: Device,
        as: 'device',
        include: [
          {
            model: Customer,
            as: 'customer',
            attributes: ['id', 'name', 'phone', 'email']
          }
        ]
      }
    ];

    // Add search functionality
    if (search) {
      includeClause[0].include[0].where = {
        [Op.or]: [
          { name: { [Op.like]: `%${search}%` } },
          { phone: { [Op.like]: `%${search}%` } }
        ]
      };
    }

    const { count, rows: serviceRequests } = await ServiceRequest.findAndCountAll({
      where: whereClause,
      limit,
      offset,
      order: [['created_at', 'DESC']],
      include: includeClause
    });

    res.json({
      serviceRequests,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(count / limit),
        totalItems: count,
        itemsPerPage: limit
      }
    });
  } catch (error) {
    console.error('Get service requests error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get service request by ID
const getServiceRequestById = async (req, res) => {
  try {
    const { id } = req.params;

    const serviceRequest = await ServiceRequest.findByPk(id, {
      include: [
        {
          model: Device,
          as: 'device',
          include: [
            {
              model: Customer,
              as: 'customer'
            }
          ]
        },
        {
          model: ServiceNote,
          as: 'serviceNotes',
          order: [['created_at', 'DESC']]
        },
        {
          model: Invoice,
          as: 'invoices'
        }
      ]
    });

    if (!serviceRequest) {
      return res.status(404).json({ message: 'Service request not found' });
    }

    // Check access permissions for technicians
    if (req.user.role === 'technician' && 
        serviceRequest.assigned_to !== req.user.name && 
        serviceRequest.assigned_to !== req.user.id.toString()) {
      return res.status(403).json({ message: 'Access denied - not assigned to this service request' });
    }

    res.json({ serviceRequest });
  } catch (error) {
    console.error('Get service request error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Create new service request
const createServiceRequest = async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { device_id, description, assigned_to, estimated_completion } = req.body;

    // Check if device exists
    const device = await Device.findByPk(device_id, {
      include: [
        {
          model: Customer,
          as: 'customer'
        }
      ]
    });

    if (!device) {
      return res.status(404).json({ message: 'Device not found' });
    }

    const serviceRequest = await ServiceRequest.create({
      device_id,
      description,
      assigned_to,
      estimated_completion,
      status: 'Pending'
    });

    // Fetch the created service request with related data
    const createdServiceRequest = await ServiceRequest.findByPk(serviceRequest.id, {
      include: [
        {
          model: Device,
          as: 'device',
          include: [
            {
              model: Customer,
              as: 'customer'
            }
          ]
        }
      ]
    });

    res.status(201).json({
      message: 'Service request created successfully',
      serviceRequest: createdServiceRequest
    });
  } catch (error) {
    console.error('Create service request error:', error);

    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        message: 'Validation failed',
        errors: error.errors.map(err => ({
          field: err.path,
          message: err.message
        }))
      });
    }

    res.status(500).json({ message: 'Internal server error' });
  }
};

// Update service request
const updateServiceRequest = async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { status, description, assigned_to, estimated_completion } = req.body;

    const serviceRequest = await ServiceRequest.findByPk(id);
    if (!serviceRequest) {
      return res.status(404).json({ message: 'Service request not found' });
    }

    // Check access permissions for technicians
    if (req.user.role === 'technician' && 
        serviceRequest.assigned_to !== req.user.name && 
        serviceRequest.assigned_to !== req.user.id.toString()) {
      return res.status(403).json({ message: 'Access denied - not assigned to this service request' });
    }

    await serviceRequest.update({
      status,
      description,
      assigned_to,
      estimated_completion
    });

    // Fetch updated service request with related data
    const updatedServiceRequest = await ServiceRequest.findByPk(id, {
      include: [
        {
          model: Device,
          as: 'device',
          include: [
            {
              model: Customer,
              as: 'customer'
            }
          ]
        }
      ]
    });

    res.json({
      message: 'Service request updated successfully',
      serviceRequest: updatedServiceRequest
    });
  } catch (error) {
    console.error('Update service request error:', error);

    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        message: 'Validation failed',
        errors: error.errors.map(err => ({
          field: err.path,
          message: err.message
        }))
      });
    }

    res.status(500).json({ message: 'Internal server error' });
  }
};

// Delete service request (soft delete)
const deleteServiceRequest = async (req, res) => {
  try {
    const { id } = req.params;

    const serviceRequest = await ServiceRequest.findByPk(id);
    if (!serviceRequest) {
      return res.status(404).json({ message: 'Service request not found' });
    }

    await serviceRequest.destroy();

    res.json({ message: 'Service request deleted successfully' });
  } catch (error) {
    console.error('Delete service request error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Assign technician to service request
const assignTechnician = async (req, res) => {
  try {
    const { id } = req.params;
    const { assigned_to } = req.body;

    const serviceRequest = await ServiceRequest.findByPk(id);
    if (!serviceRequest) {
      return res.status(404).json({ message: 'Service request not found' });
    }

    await serviceRequest.update({
      assigned_to,
      status: 'Assigned'
    });

    res.json({
      message: 'Technician assigned successfully',
      serviceRequest
    });
  } catch (error) {
    console.error('Assign technician error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Update service request status
const updateStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const serviceRequest = await ServiceRequest.findByPk(id);
    if (!serviceRequest) {
      return res.status(404).json({ message: 'Service request not found' });
    }

    // Check access permissions for technicians
    if (req.user.role === 'technician' && 
        serviceRequest.assigned_to !== req.user.name && 
        serviceRequest.assigned_to !== req.user.id.toString()) {
      return res.status(403).json({ message: 'Access denied - not assigned to this service request' });
    }

    await serviceRequest.update({ status });

    res.json({
      message: 'Status updated successfully',
      serviceRequest
    });
  } catch (error) {
    console.error('Update status error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

module.exports = {
  getAllServiceRequests,
  getServiceRequestById,
  createServiceRequest,
  updateServiceRequest,
  deleteServiceRequest,
  assignTechnician,
  updateStatus
};
