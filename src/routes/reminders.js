const express = require('express');
const { body, param, query } = require('express-validator');
const reminderController = require('../controllers/reminderController');
const { authenticateToken, requireCustomerService } = require('../middleware/auth');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

/**
 * @swagger
 * tags:
 *   name: Reminders
 *   description: Reminder management operations
 */

/**
 * @swagger
 * /reminders:
 *   get:
 *     summary: Get all reminders with pagination and filtering
 *     tags: [Reminders]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: is_sent
 *         schema:
 *           type: boolean
 *         description: Filter by sent status
 *       - in: query
 *         name: date_from
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter reminders from this date
 *       - in: query
 *         name: date_to
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter reminders to this date
 *       - in: query
 *         name: customer_id
 *         schema:
 *           type: integer
 *         description: Filter by customer ID
 *     responses:
 *       200:
 *         description: List of reminders retrieved successfully
 */
router.get('/', [
  requireCustomerService,
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('is_sent').optional().isBoolean().withMessage('Is sent must be a boolean'),
  query('date_from').optional().isISO8601().withMessage('Date from must be a valid date'),
  query('date_to').optional().isISO8601().withMessage('Date to must be a valid date'),
  query('customer_id').optional().isInt({ min: 1 }).withMessage('Customer ID must be a positive integer')
], reminderController.getAllReminders);

/**
 * @swagger
 * /reminders/due:
 *   get:
 *     summary: Get due reminders (today or overdue)
 *     tags: [Reminders]
 *     responses:
 *       200:
 *         description: Due reminders retrieved successfully
 */
router.get('/due', [
  requireCustomerService
], reminderController.getDueReminders);

/**
 * @swagger
 * /reminders/{id}:
 *   get:
 *     summary: Get reminder by ID
 *     tags: [Reminders]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Reminder ID
 *     responses:
 *       200:
 *         description: Reminder retrieved successfully
 *       404:
 *         description: Reminder not found
 */
router.get('/:id', [
  requireCustomerService,
  param('id').isInt({ min: 1 }).withMessage('Reminder ID must be a positive integer')
], reminderController.getReminderById);

/**
 * @swagger
 * /reminders:
 *   post:
 *     summary: Create a new reminder
 *     tags: [Reminders]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - customer_id
 *               - title
 *               - remind_date
 *             properties:
 *               customer_id:
 *                 type: integer
 *                 description: Customer ID
 *               title:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 200
 *                 description: Reminder title
 *               note:
 *                 type: string
 *                 maxLength: 1000
 *                 description: Additional notes
 *               remind_date:
 *                 type: string
 *                 format: date
 *                 description: Date when reminder should be triggered
 *     responses:
 *       201:
 *         description: Reminder created successfully
 *       400:
 *         description: Validation error
 *       404:
 *         description: Customer not found
 */
router.post('/', [
  requireCustomerService,
  body('customer_id')
    .isInt({ min: 1 })
    .withMessage('Customer ID must be a positive integer'),
  body('title')
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Title must be between 1 and 200 characters'),
  body('note')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Note must be less than 1000 characters'),
  body('remind_date')
    .isISO8601()
    .withMessage('Remind date must be a valid date')
], reminderController.createReminder);

/**
 * @swagger
 * /reminders/{id}:
 *   put:
 *     summary: Update reminder
 *     tags: [Reminders]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Reminder ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               customer_id:
 *                 type: integer
 *                 description: Customer ID
 *               title:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 200
 *                 description: Reminder title
 *               note:
 *                 type: string
 *                 maxLength: 1000
 *                 description: Additional notes
 *               remind_date:
 *                 type: string
 *                 format: date
 *                 description: Date when reminder should be triggered
 *               is_sent:
 *                 type: boolean
 *                 description: Whether reminder has been sent
 *     responses:
 *       200:
 *         description: Reminder updated successfully
 *       400:
 *         description: Validation error
 *       404:
 *         description: Reminder or customer not found
 */
router.put('/:id', [
  requireCustomerService,
  param('id').isInt({ min: 1 }).withMessage('Reminder ID must be a positive integer'),
  body('customer_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Customer ID must be a positive integer'),
  body('title')
    .optional()
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Title must be between 1 and 200 characters'),
  body('note')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Note must be less than 1000 characters'),
  body('remind_date')
    .optional()
    .isISO8601()
    .withMessage('Remind date must be a valid date'),
  body('is_sent')
    .optional()
    .isBoolean()
    .withMessage('Is sent must be a boolean')
], reminderController.updateReminder);

/**
 * @swagger
 * /reminders/{id}:
 *   delete:
 *     summary: Delete reminder (soft delete)
 *     tags: [Reminders]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Reminder ID
 *     responses:
 *       200:
 *         description: Reminder deleted successfully
 *       404:
 *         description: Reminder not found
 */
router.delete('/:id', [
  requireCustomerService,
  param('id').isInt({ min: 1 }).withMessage('Reminder ID must be a positive integer')
], reminderController.deleteReminder);

/**
 * @swagger
 * /reminders/{id}/mark-sent:
 *   put:
 *     summary: Mark reminder as sent
 *     tags: [Reminders]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Reminder ID
 *     responses:
 *       200:
 *         description: Reminder marked as sent successfully
 *       404:
 *         description: Reminder not found
 */
router.put('/:id/marksent', [
  requireCustomerService,
  param('id').isInt({ min: 1 }).withMessage('Reminder ID must be a positive integer')
], reminderController.markAsSent);

module.exports = router;
