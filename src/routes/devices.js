const express = require('express');
const { body, param, query } = require('express-validator');
const deviceController = require('../controllers/deviceController');
const { authenticateToken, requireCustomerService } = require('../middleware/auth');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

/**
 * @swagger
 * tags:
 *   name: Devices
 *   description: Device management operations
 */

/**
 * @swagger
 * /devices:
 *   get:
 *     summary: Get all devices with pagination and search
 *     tags: [Devices]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for brand, model, or serial number
 *       - in: query
 *         name: customer_id
 *         schema:
 *           type: integer
 *         description: Filter by customer ID
 *     responses:
 *       200:
 *         description: List of devices retrieved successfully
 */
router.get('/', [
  requireCustomerService,
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('search').optional().isString().trim(),
  query('customer_id').optional().isInt({ min: 1 }).withMessage('Customer ID must be a positive integer')
], deviceController.getAllDevices);

/**
 * @swagger
 * /devices/{id}:
 *   get:
 *     summary: Get device by ID
 *     tags: [Devices]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Device ID
 *     responses:
 *       200:
 *         description: Device retrieved successfully
 *       404:
 *         description: Device not found
 */
router.get('/:id', [
  requireCustomerService,
  param('id').isInt({ min: 1 }).withMessage('Device ID must be a positive integer')
], deviceController.getDeviceById);

/**
 * @swagger
 * /devices:
 *   post:
 *     summary: Create a new device
 *     tags: [Devices]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - customer_id
 *               - brand
 *               - model
 *             properties:
 *               customer_id:
 *                 type: integer
 *               brand:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 50
 *               model:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 100
 *               serial_number:
 *                 type: string
 *                 maxLength: 100
 *               warranty_expiry:
 *                 type: string
 *                 format: date
 *     responses:
 *       201:
 *         description: Device created successfully
 *       400:
 *         description: Validation error
 *       404:
 *         description: Customer not found
 *       409:
 *         description: Serial number already exists
 */
router.post('/', [
  requireCustomerService,
  body('customer_id')
    .isInt({ min: 1 })
    .withMessage('Customer ID must be a positive integer'),
  body('brand')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Brand must be between 1 and 50 characters'),
  body('model')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Model must be between 1 and 100 characters'),
  body('serial_number')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Serial number must be less than 100 characters'),
  body('warranty_expiry')
    .optional()
    .isISO8601()
    .withMessage('Warranty expiry must be a valid date')
], deviceController.createDevice);

/**
 * @swagger
 * /devices/{id}:
 *   put:
 *     summary: Update device
 *     tags: [Devices]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Device ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               customer_id:
 *                 type: integer
 *               brand:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 50
 *               model:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 100
 *               serial_number:
 *                 type: string
 *                 maxLength: 100
 *               warranty_expiry:
 *                 type: string
 *                 format: date
 *     responses:
 *       200:
 *         description: Device updated successfully
 *       400:
 *         description: Validation error
 *       404:
 *         description: Device or customer not found
 *       409:
 *         description: Serial number already exists
 */
router.put('/:id', [
  requireCustomerService,
  param('id').isInt({ min: 1 }).withMessage('Device ID must be a positive integer'),
  body('customer_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Customer ID must be a positive integer'),
  body('brand')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Brand must be between 1 and 50 characters'),
  body('model')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Model must be between 1 and 100 characters'),
  body('serial_number')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Serial number must be less than 100 characters'),
  body('warranty_expiry')
    .optional()
    .isISO8601()
    .withMessage('Warranty expiry must be a valid date')
], deviceController.updateDevice);

/**
 * @swagger
 * /devices/{id}:
 *   delete:
 *     summary: Delete device (soft delete)
 *     tags: [Devices]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Device ID
 *     responses:
 *       200:
 *         description: Device deleted successfully
 *       404:
 *         description: Device not found
 */
router.delete('/:id', [
  requireCustomerService,
  param('id').isInt({ min: 1 }).withMessage('Device ID must be a positive integer')
], deviceController.deleteDevice);

/**
 * @swagger
 * /devices/customer/{customerId}:
 *   get:
 *     summary: Get devices by customer ID
 *     tags: [Devices]
 *     parameters:
 *       - in: path
 *         name: customerId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Customer ID
 *     responses:
 *       200:
 *         description: Customer devices retrieved successfully
 *       404:
 *         description: Customer not found
 */
router.get('/customer/:customerId', [
  requireCustomerService,
  param('customerId').isInt({ min: 1 }).withMessage('Customer ID must be a positive integer')
], deviceController.getDevicesByCustomer);

module.exports = router;
