const express = require('express');
const { body, param, query } = require('express-validator');
const invoiceController = require('../controllers/invoiceController');
const { authenticateToken, requireCustomerService } = require('../middleware/auth');
const { uploadPDF, handleUploadError, processPDFUpload } = require('../middleware/upload');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

/**
 * @swagger
 * tags:
 *   name: Invoices
 *   description: Invoice management operations
 */

/**
 * @swagger
 * /invoices:
 *   get:
 *     summary: Get all invoices with pagination and filtering
 *     tags: [Invoices]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: paid
 *         schema:
 *           type: boolean
 *         description: Filter by payment status
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search in customer name or phone
 *     responses:
 *       200:
 *         description: List of invoices retrieved successfully
 */
router.get('/', [
  requireCustomerService,
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('paid').optional().isBoolean().withMessage('Paid must be a boolean'),
  query('search').optional().isString().trim()
], invoiceController.getAllInvoices);

/**
 * @swagger
 * /invoices/{id}:
 *   get:
 *     summary: Get invoice by ID
 *     tags: [Invoices]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Invoice ID
 *     responses:
 *       200:
 *         description: Invoice retrieved successfully
 *       404:
 *         description: Invoice not found
 */
router.get('/:id', [
  requireCustomerService,
  param('id').isInt({ min: 1 }).withMessage('Invoice ID must be a positive integer')
], invoiceController.getInvoiceById);

/**
 * @swagger
 * /invoices:
 *   post:
 *     summary: Create a new invoice
 *     tags: [Invoices]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - service_request_id
 *               - amount
 *             properties:
 *               service_request_id:
 *                 type: integer
 *                 description: Service request ID
 *               amount:
 *                 type: number
 *                 format: decimal
 *                 minimum: 0
 *                 description: Invoice amount
 *               due_date:
 *                 type: string
 *                 format: date
 *                 description: Payment due date
 *               pdf:
 *                 type: string
 *                 format: binary
 *                 description: Optional invoice PDF file
 *     responses:
 *       201:
 *         description: Invoice created successfully
 *       400:
 *         description: Validation error or file upload error
 *       404:
 *         description: Service request not found
 */
router.post('/', [
  requireCustomerService,
  uploadPDF,
  handleUploadError,
  processPDFUpload,
  body('service_request_id')
    .isInt({ min: 1 })
    .withMessage('Service request ID must be a positive integer'),
  body('amount')
    .isFloat({ min: 0 })
    .withMessage('Amount must be a positive number'),
  body('due_date')
    .optional()
    .isISO8601()
    .withMessage('Due date must be a valid date')
], invoiceController.createInvoice);

/**
 * @swagger
 * /invoices/{id}:
 *   put:
 *     summary: Update invoice
 *     tags: [Invoices]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Invoice ID
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               amount:
 *                 type: number
 *                 format: decimal
 *                 minimum: 0
 *                 description: Invoice amount
 *               paid:
 *                 type: boolean
 *                 description: Payment status
 *               due_date:
 *                 type: string
 *                 format: date
 *                 description: Payment due date
 *               pdf:
 *                 type: string
 *                 format: binary
 *                 description: Optional invoice PDF file - replaces existing PDF
 *     responses:
 *       200:
 *         description: Invoice updated successfully
 *       400:
 *         description: Validation error or file upload error
 *       404:
 *         description: Invoice not found
 */
router.put('/:id', [
  requireCustomerService,
  uploadPDF,
  handleUploadError,
  processPDFUpload,
  param('id').isInt({ min: 1 }).withMessage('Invoice ID must be a positive integer'),
  body('amount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Amount must be a positive number'),
  body('paid')
    .optional()
    .isBoolean()
    .withMessage('Paid must be a boolean'),
  body('due_date')
    .optional()
    .isISO8601()
    .withMessage('Due date must be a valid date')
], invoiceController.updateInvoice);

/**
 * @swagger
 * /invoices/{id}:
 *   delete:
 *     summary: Delete invoice (soft delete)
 *     tags: [Invoices]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Invoice ID
 *     responses:
 *       200:
 *         description: Invoice deleted successfully
 *       404:
 *         description: Invoice not found
 */
router.delete('/:id', [
  requireCustomerService,
  param('id').isInt({ min: 1 }).withMessage('Invoice ID must be a positive integer')
], invoiceController.deleteInvoice);

/**
 * @swagger
 * /invoices/{id}/mark-paid:
 *   put:
 *     summary: Mark invoice as paid
 *     tags: [Invoices]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Invoice ID
 *     responses:
 *       200:
 *         description: Invoice marked as paid successfully
 *       404:
 *         description: Invoice not found
 */
router.put('/:id/markpaid', [
  requireCustomerService,
  param('id').isInt({ min: 1 }).withMessage('Invoice ID must be a positive integer')
], invoiceController.markAsPaid);

module.exports = router;
