const express = require('express');
const { body, param } = require('express-validator');
const serviceNoteController = require('../controllers/serviceNoteController');
const { authenticateToken, requireAnyRole } = require('../middleware/auth');
const { uploadPhoto, handleUploadError, processPhotoUpload } = require('../middleware/upload');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

/**
 * @swagger
 * tags:
 *   name: Service Notes
 *   description: Service note management operations
 */

/**
 * @swagger
 * /service-notes/service-request/{serviceRequestId}:
 *   get:
 *     summary: Get all service notes for a service request
 *     tags: [Service Notes]
 *     parameters:
 *       - in: path
 *         name: serviceRequestId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Service request ID
 *     responses:
 *       200:
 *         description: Service notes retrieved successfully
 *       403:
 *         description: Access denied (for technicians not assigned to this request)
 *       404:
 *         description: Service request not found
 */
router.get('/service-request/:serviceRequestId', [
  requireAnyRole,
  param('serviceRequestId').isInt({ min: 1 }).withMessage('Service request ID must be a positive integer')
], serviceNoteController.getServiceNotesByRequest);

/**
 * @swagger
 * /service-notes/{id}:
 *   get:
 *     summary: Get service note by ID
 *     tags: [Service Notes]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Service note ID
 *     responses:
 *       200:
 *         description: Service note retrieved successfully
 *       403:
 *         description: Access denied
 *       404:
 *         description: Service note not found
 */
router.get('/:id', [
  requireAnyRole,
  param('id').isInt({ min: 1 }).withMessage('Service note ID must be a positive integer')
], serviceNoteController.getServiceNoteById);

/**
 * @swagger
 * /service-notes:
 *   post:
 *     summary: Create a new service note
 *     tags: [Service Notes]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - service_request_id
 *               - note
 *             properties:
 *               service_request_id:
 *                 type: integer
 *                 description: Service request ID
 *               note:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 2000
 *                 description: Note content
 *               photo:
 *                 type: string
 *                 format: binary
 *                 description: Optional photo file (JPEG, PNG, GIF, WebP)
 *     responses:
 *       201:
 *         description: Service note created successfully
 *       400:
 *         description: Validation error or file upload error
 *       403:
 *         description: Access denied
 *       404:
 *         description: Service request not found
 */
router.post('/', [
  requireAnyRole,
  uploadPhoto,
  handleUploadError,
  processPhotoUpload,
  body('service_request_id')
    .isInt({ min: 1 })
    .withMessage('Service request ID must be a positive integer'),
  body('note')
    .trim()
    .isLength({ min: 1, max: 2000 })
    .withMessage('Note must be between 1 and 2000 characters')
], serviceNoteController.createServiceNote);

/**
 * @swagger
 * /service-notes/{id}:
 *   put:
 *     summary: Update service note
 *     tags: [Service Notes]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Service note ID
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - note
 *             properties:
 *               note:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 2000
 *                 description: Note content
 *               photo:
 *                 type: string
 *                 format: binary
 *                 description: Optional photo file (JPEG, PNG, GIF, WebP) - replaces existing photo
 *     responses:
 *       200:
 *         description: Service note updated successfully
 *       400:
 *         description: Validation error or file upload error
 *       403:
 *         description: Access denied
 *       404:
 *         description: Service note not found
 */
router.put('/:id', [
  requireAnyRole,
  uploadPhoto,
  handleUploadError,
  processPhotoUpload,
  param('id').isInt({ min: 1 }).withMessage('Service note ID must be a positive integer'),
  body('note')
    .trim()
    .isLength({ min: 1, max: 2000 })
    .withMessage('Note must be between 1 and 2000 characters')
], serviceNoteController.updateServiceNote);

/**
 * @swagger
 * /service-notes/{id}:
 *   delete:
 *     summary: Delete service note (soft delete)
 *     tags: [Service Notes]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Service note ID
 *     responses:
 *       200:
 *         description: Service note deleted successfully
 *       403:
 *         description: Access denied
 *       404:
 *         description: Service note not found
 */
router.delete('/:id', [
  requireAnyRole,
  param('id').isInt({ min: 1 }).withMessage('Service note ID must be a positive integer')
], serviceNoteController.deleteServiceNote);

module.exports = router;
