const express = require('express');
const { body, param, query } = require('express-validator');
const serviceRequestController = require('../controllers/serviceRequestController');
const { authenticateToken, requireAnyRole, requireCustomerService } = require('../middleware/auth');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

/**
 * @swagger
 * tags:
 *   name: Service Requests
 *   description: Service request management operations
 */

/**
 * @swagger
 * /service-requests:
 *   get:
 *     summary: Get all service requests with pagination and filtering
 *     tags: [Service Requests]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [Pending, Assigned, OnTheWay, InRepair, Completed]
 *         description: Filter by status
 *       - in: query
 *         name: assigned_to
 *         schema:
 *           type: string
 *         description: Filter by assigned technician
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search in customer name or phone
 *     responses:
 *       200:
 *         description: List of service requests retrieved successfully
 */
router.get('/', [
  requireAnyRole,
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('status').optional().isIn(['Pending', 'Assigned', 'OnTheWay', 'InRepair', 'Completed']).withMessage('Invalid status'),
  query('assigned_to').optional().isString().trim(),
  query('search').optional().isString().trim()
], serviceRequestController.getAllServiceRequests);

/**
 * @swagger
 * /service-requests/{id}:
 *   get:
 *     summary: Get service request by ID
 *     tags: [Service Requests]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Service request ID
 *     responses:
 *       200:
 *         description: Service request retrieved successfully
 *       403:
 *         description: Access denied (for technicians not assigned to this request)
 *       404:
 *         description: Service request not found
 */
router.get('/:id', [
  requireAnyRole,
  param('id').isInt({ min: 1 }).withMessage('Service request ID must be a positive integer')
], serviceRequestController.getServiceRequestById);

/**
 * @swagger
 * /service-requests:
 *   post:
 *     summary: Create a new service request
 *     tags: [Service Requests]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - device_id
 *               - description
 *             properties:
 *               device_id:
 *                 type: integer
 *               description:
 *                 type: string
 *                 minLength: 10
 *                 maxLength: 2000
 *               assigned_to:
 *                 type: string
 *                 maxLength: 100
 *               estimated_completion:
 *                 type: string
 *                 format: date
 *     responses:
 *       201:
 *         description: Service request created successfully
 *       400:
 *         description: Validation error
 *       404:
 *         description: Device not found
 */
router.post('/', [
  requireCustomerService,
  body('device_id')
    .isInt({ min: 1 })
    .withMessage('Device ID must be a positive integer'),
  body('description')
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage('Description must be between 10 and 2000 characters'),
  body('assigned_to')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Assigned to must be less than 100 characters'),
  body('estimated_completion')
    .optional()
    .isISO8601()
    .withMessage('Estimated completion must be a valid date')
], serviceRequestController.createServiceRequest);

/**
 * @swagger
 * /service-requests/{id}:
 *   put:
 *     summary: Update service request
 *     tags: [Service Requests]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Service request ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [Pending, Assigned, OnTheWay, InRepair, Completed]
 *               description:
 *                 type: string
 *                 minLength: 10
 *                 maxLength: 2000
 *               assigned_to:
 *                 type: string
 *                 maxLength: 100
 *               estimated_completion:
 *                 type: string
 *                 format: date
 *     responses:
 *       200:
 *         description: Service request updated successfully
 *       400:
 *         description: Validation error
 *       403:
 *         description: Access denied
 *       404:
 *         description: Service request not found
 */
router.put('/:id', [
  requireAnyRole,
  param('id').isInt({ min: 1 }).withMessage('Service request ID must be a positive integer'),
  body('status')
    .optional()
    .isIn(['Pending', 'Assigned', 'OnTheWay', 'InRepair', 'Completed'])
    .withMessage('Invalid status'),
  body('description')
    .optional()
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage('Description must be between 10 and 2000 characters'),
  body('assigned_to')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Assigned to must be less than 100 characters'),
  body('estimated_completion')
    .optional()
    .isISO8601()
    .withMessage('Estimated completion must be a valid date')
], serviceRequestController.updateServiceRequest);

/**
 * @swagger
 * /service-requests/{id}:
 *   delete:
 *     summary: Delete service request (soft delete)
 *     tags: [Service Requests]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Service request ID
 *     responses:
 *       200:
 *         description: Service request deleted successfully
 *       404:
 *         description: Service request not found
 */
router.delete('/:id', [
  requireCustomerService,
  param('id').isInt({ min: 1 }).withMessage('Service request ID must be a positive integer')
], serviceRequestController.deleteServiceRequest);

/**
 * @swagger
 * /service-requests/{id}/assign:
 *   put:
 *     summary: Assign technician to service request
 *     tags: [Service Requests]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Service request ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - assigned_to
 *             properties:
 *               assigned_to:
 *                 type: string
 *                 maxLength: 100
 *     responses:
 *       200:
 *         description: Technician assigned successfully
 *       404:
 *         description: Service request not found
 */
router.put('/:id/assign', [
  requireCustomerService,
  param('id').isInt({ min: 1 }).withMessage('Service request ID must be a positive integer'),
  body('assigned_to')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Assigned to is required and must be less than 100 characters')
], serviceRequestController.assignTechnician);

/**
 * @swagger
 * /service-requests/{id}/status:
 *   put:
 *     summary: Update service request status
 *     tags: [Service Requests]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Service request ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [Pending, Assigned, OnTheWay, InRepair, Completed]
 *     responses:
 *       200:
 *         description: Status updated successfully
 *       403:
 *         description: Access denied
 *       404:
 *         description: Service request not found
 */
router.put('/:id/status', [
  requireAnyRole,
  param('id').isInt({ min: 1 }).withMessage('Service request ID must be a positive integer'),
  body('status')
    .isIn(['Pending', 'Assigned', 'OnTheWay', 'InRepair', 'Completed'])
    .withMessage('Invalid status')
], serviceRequestController.updateStatus);

module.exports = router;
