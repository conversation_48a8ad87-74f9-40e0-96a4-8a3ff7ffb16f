const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * @swagger
 * components:
 *   schemas:
 *     Customer:
 *       type: object
 *       required:
 *         - name
 *         - phone
 *       properties:
 *         id:
 *           type: integer
 *           description: Auto-generated customer ID
 *         name:
 *           type: string
 *           description: Customer's full name
 *         phone:
 *           type: string
 *           description: Customer's phone number (unique)
 *         email:
 *           type: string
 *           format: email
 *           description: Customer's email address (optional)
 *         address:
 *           type: string
 *           description: Customer's address (optional)
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *         deleted_at:
 *           type: string
 *           format: date-time
 */

const Customer = sequelize.define('Customer', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Customer name is required'
      },
      len: {
        args: [2, 100],
        msg: 'Customer name must be between 2 and 100 characters'
      }
    }
  },
  phone: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: {
      msg: 'Phone number already exists'
    },
    validate: {
      notEmpty: {
        msg: 'Phone number is required'
      },
      is: {
        args: /^[\+]?[0-9\s\-\(\)]{10,20}$/,
        msg: 'Phone number format is invalid'
      }
    }
  },
  email: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      isEmail: {
        msg: 'Must be a valid email address'
      }
    }
  },
  address: {
    type: DataTypes.TEXT,
    allowNull: true
  }
}, {
  tableName: 'customers',
  indexes: [
    {
      unique: true,
      fields: ['phone']
    },
    {
      fields: ['name']
    },
    {
      fields: ['email']
    }
  ]
});

module.exports = Customer;
