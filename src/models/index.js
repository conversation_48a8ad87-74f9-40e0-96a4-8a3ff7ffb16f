const { sequelize } = require('../config/database');

// Import all models
const User = require('./User');
const Customer = require('./Customer');
const Device = require('./Device');
const ServiceRequest = require('./ServiceRequest');
const ServiceNote = require('./ServiceNote');
const Invoice = require('./Invoice');
const Reminder = require('./Reminder');

// Define associations

// Customer -> Device (One-to-Many)
Customer.hasMany(Device, {
  foreignKey: 'customer_id',
  as: 'devices',
  onDelete: 'CASCADE'
});
Device.belongsTo(Customer, {
  foreignKey: 'customer_id',
  as: 'customer'
});

// Device -> ServiceRequest (One-to-Many)
Device.hasMany(ServiceRequest, {
  foreignKey: 'device_id',
  as: 'serviceRequests',
  onDelete: 'CASCADE'
});
ServiceRequest.belongsTo(Device, {
  foreignKey: 'device_id',
  as: 'device'
});

// ServiceRequest -> ServiceNote (One-to-Many)
ServiceRequest.hasMany(ServiceNote, {
  foreignKey: 'service_request_id',
  as: 'serviceNotes',
  onDelete: 'CASCADE'
});
ServiceNote.belongsTo(ServiceRequest, {
  foreignKey: 'service_request_id',
  as: 'serviceRequest'
});

// ServiceRequest -> Invoice (One-to-Many)
ServiceRequest.hasMany(Invoice, {
  foreignKey: 'service_request_id',
  as: 'invoices',
  onDelete: 'CASCADE'
});
Invoice.belongsTo(ServiceRequest, {
  foreignKey: 'service_request_id',
  as: 'serviceRequest'
});

// Customer -> Reminder (One-to-Many)
Customer.hasMany(Reminder, {
  foreignKey: 'customer_id',
  as: 'reminders',
  onDelete: 'CASCADE'
});
Reminder.belongsTo(Customer, {
  foreignKey: 'customer_id',
  as: 'customer'
});

// Additional convenience associations for easier querying

// Customer -> ServiceRequest (through Device)
Customer.hasMany(ServiceRequest, {
  through: Device,
  foreignKey: 'customer_id',
  otherKey: 'device_id',
  as: 'allServiceRequests'
});

// Customer -> Invoice (through Device and ServiceRequest)
Customer.hasMany(Invoice, {
  through: [Device, ServiceRequest],
  as: 'allInvoices'
});

// Export all models and sequelize instance
module.exports = {
  sequelize,
  User,
  Customer,
  Device,
  ServiceRequest,
  ServiceNote,
  Invoice,
  Reminder
};
