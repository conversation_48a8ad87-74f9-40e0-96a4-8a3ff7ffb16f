const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * @swagger
 * components:
 *   schemas:
 *     ServiceRequest:
 *       type: object
 *       required:
 *         - device_id
 *         - description
 *       properties:
 *         id:
 *           type: integer
 *           description: Auto-generated service request ID
 *         device_id:
 *           type: integer
 *           description: ID of the device requiring service
 *         status:
 *           type: string
 *           enum: [Pending, Assigned, OnTheWay, InRepair, Completed]
 *           description: Current status of the service request
 *         description:
 *           type: string
 *           description: Description of the service request/problem
 *         assigned_to:
 *           type: string
 *           description: Technician name or ID assigned to this request
 *         request_date:
 *           type: string
 *           format: date
 *           description: Date when the request was made
 *         estimated_completion:
 *           type: string
 *           format: date
 *           description: Estimated completion date
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *         deleted_at:
 *           type: string
 *           format: date-time
 */

const ServiceRequest = sequelize.define('ServiceRequest', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  device_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'devices',
      key: 'id'
    },
    validate: {
      notEmpty: {
        msg: 'Device ID is required'
      }
    }
  },
  status: {
    type: DataTypes.ENUM('Pending', 'Assigned', 'OnTheWay', 'InRepair', 'Completed'),
    allowNull: false,
    defaultValue: 'Pending',
    validate: {
      isIn: {
        args: [['Pending', 'Assigned', 'OnTheWay', 'InRepair', 'Completed']],
        msg: 'Status must be one of: Pending, Assigned, OnTheWay, InRepair, Completed'
      }
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Service description is required'
      },
      len: {
        args: [10, 2000],
        msg: 'Description must be between 10 and 2000 characters'
      }
    }
  },
  assigned_to: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: {
        args: [0, 100],
        msg: 'Assigned to must be less than 100 characters'
      }
    }
  },
  request_date: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    validate: {
      isDate: {
        msg: 'Request date must be a valid date'
      }
    }
  },
  estimated_completion: {
    type: DataTypes.DATEONLY,
    allowNull: true,
    validate: {
      isDate: {
        msg: 'Estimated completion must be a valid date'
      },
      isAfterRequestDate(value) {
        if (value && this.request_date && new Date(value) < new Date(this.request_date)) {
          throw new Error('Estimated completion date cannot be before request date');
        }
      }
    }
  }
}, {
  tableName: 'service_requests',
  indexes: [
    {
      fields: ['device_id']
    },
    {
      fields: ['status']
    },
    {
      fields: ['assigned_to']
    },
    {
      fields: ['request_date']
    },
    {
      fields: ['estimated_completion']
    }
  ]
});

module.exports = ServiceRequest;
