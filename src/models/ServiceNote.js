const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * @swagger
 * components:
 *   schemas:
 *     ServiceNote:
 *       type: object
 *       required:
 *         - service_request_id
 *         - note
 *         - created_by
 *       properties:
 *         id:
 *           type: integer
 *           description: Auto-generated service note ID
 *         service_request_id:
 *           type: integer
 *           description: ID of the service request this note belongs to
 *         note:
 *           type: string
 *           description: The service note content
 *         photo:
 *           type: string
 *           description: Path to uploaded photo file (optional)
 *         created_by:
 *           type: string
 *           description: Name or ID of the user who created this note
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *         deleted_at:
 *           type: string
 *           format: date-time
 */

const ServiceNote = sequelize.define('ServiceNote', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  service_request_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'service_requests',
      key: 'id'
    },
    validate: {
      notEmpty: {
        msg: 'Service request ID is required'
      }
    }
  },
  note: {
    type: DataTypes.TEXT,
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Note content is required'
      },
      len: {
        args: [1, 2000],
        msg: 'Note must be between 1 and 2000 characters'
      }
    }
  },
  photo: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: {
        args: [0, 255],
        msg: 'Photo path must be less than 255 characters'
      }
    }
  },
  created_by: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Created by is required'
      },
      len: {
        args: [1, 100],
        msg: 'Created by must be between 1 and 100 characters'
      }
    }
  }
}, {
  tableName: 'service_notes',
  indexes: [
    {
      fields: ['service_request_id']
    },
    {
      fields: ['created_by']
    },
    {
      fields: ['created_at']
    }
  ]
});

module.exports = ServiceNote;
