const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * @swagger
 * components:
 *   schemas:
 *     Invoice:
 *       type: object
 *       required:
 *         - service_request_id
 *         - amount
 *       properties:
 *         id:
 *           type: integer
 *           description: Auto-generated invoice ID
 *         service_request_id:
 *           type: integer
 *           description: ID of the service request this invoice is for
 *         amount:
 *           type: number
 *           format: decimal
 *           description: Invoice amount
 *         paid:
 *           type: boolean
 *           description: Payment status
 *         issue_date:
 *           type: string
 *           format: date
 *           description: Date when the invoice was issued
 *         due_date:
 *           type: string
 *           format: date
 *           description: Payment due date (optional)
 *         invoice_pdf:
 *           type: string
 *           description: Path to uploaded invoice PDF file (optional)
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *         deleted_at:
 *           type: string
 *           format: date-time
 */

const Invoice = sequelize.define('Invoice', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  service_request_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'service_requests',
      key: 'id'
    },
    validate: {
      notEmpty: {
        msg: 'Service request ID is required'
      }
    }
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Invoice amount is required'
      },
      min: {
        args: [0],
        msg: 'Amount must be greater than or equal to 0'
      },
      max: {
        args: [99999999.99],
        msg: 'Amount cannot exceed 99,999,999.99'
      }
    }
  },
  paid: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  issue_date: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    validate: {
      isDate: {
        msg: 'Issue date must be a valid date'
      }
    }
  },
  due_date: {
    type: DataTypes.DATEONLY,
    allowNull: true,
    validate: {
      isDate: {
        msg: 'Due date must be a valid date'
      },
      isAfterIssueDate(value) {
        if (value && this.issue_date && new Date(value) < new Date(this.issue_date)) {
          throw new Error('Due date cannot be before issue date');
        }
      }
    }
  },
  invoice_pdf: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: {
        args: [0, 255],
        msg: 'Invoice PDF path must be less than 255 characters'
      }
    }
  }
}, {
  tableName: 'invoices',
  indexes: [
    {
      fields: ['service_request_id']
    },
    {
      fields: ['paid']
    },
    {
      fields: ['issue_date']
    },
    {
      fields: ['due_date']
    },
    {
      fields: ['amount']
    }
  ]
});

module.exports = Invoice;
