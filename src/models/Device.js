const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * @swagger
 * components:
 *   schemas:
 *     Device:
 *       type: object
 *       required:
 *         - customer_id
 *         - brand
 *         - model
 *       properties:
 *         id:
 *           type: integer
 *           description: Auto-generated device ID
 *         customer_id:
 *           type: integer
 *           description: ID of the customer who owns this device
 *         brand:
 *           type: string
 *           description: Device brand/manufacturer
 *         model:
 *           type: string
 *           description: Device model
 *         serial_number:
 *           type: string
 *           description: Device serial number (unique, optional)
 *         warranty_expiry:
 *           type: string
 *           format: date
 *           description: Warranty expiration date (optional)
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *         deleted_at:
 *           type: string
 *           format: date-time
 */

const Device = sequelize.define('Device', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  customer_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'customers',
      key: 'id'
    },
    validate: {
      notEmpty: {
        msg: 'Customer ID is required'
      }
    }
  },
  brand: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Device brand is required'
      },
      len: {
        args: [1, 50],
        msg: 'Brand must be between 1 and 50 characters'
      }
    }
  },
  model: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Device model is required'
      },
      len: {
        args: [1, 100],
        msg: 'Model must be between 1 and 100 characters'
      }
    }
  },
  serial_number: {
    type: DataTypes.STRING,
    allowNull: true,
    unique: {
      msg: 'Serial number already exists'
    },
    validate: {
      len: {
        args: [0, 100],
        msg: 'Serial number must be less than 100 characters'
      }
    }
  },
  warranty_expiry: {
    type: DataTypes.DATEONLY,
    allowNull: true,
    validate: {
      isDate: {
        msg: 'Warranty expiry must be a valid date'
      }
    }
  }
}, {
  tableName: 'devices',
  indexes: [
    {
      unique: true,
      fields: ['serial_number'],
      where: {
        serial_number: {
          [sequelize.Sequelize.Op.ne]: null
        }
      }
    },
    {
      fields: ['customer_id']
    },
    {
      fields: ['brand', 'model']
    }
  ]
});

module.exports = Device;
