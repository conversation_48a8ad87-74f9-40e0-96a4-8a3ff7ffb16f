const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * @swagger
 * components:
 *   schemas:
 *     Reminder:
 *       type: object
 *       required:
 *         - customer_id
 *         - title
 *         - remind_date
 *       properties:
 *         id:
 *           type: integer
 *           description: Auto-generated reminder ID
 *         customer_id:
 *           type: integer
 *           description: ID of the customer this reminder is for
 *         title:
 *           type: string
 *           description: Reminder title
 *         note:
 *           type: string
 *           description: Additional reminder notes (optional)
 *         remind_date:
 *           type: string
 *           format: date
 *           description: Date when the reminder should be triggered
 *         is_sent:
 *           type: boolean
 *           description: Whether the reminder has been sent
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *         deleted_at:
 *           type: string
 *           format: date-time
 */

const Reminder = sequelize.define('Reminder', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  customer_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'customers',
      key: 'id'
    },
    validate: {
      notEmpty: {
        msg: 'Customer ID is required'
      }
    }
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Reminder title is required'
      },
      len: {
        args: [1, 200],
        msg: 'Title must be between 1 and 200 characters'
      }
    }
  },
  note: {
    type: DataTypes.TEXT,
    allowNull: true,
    validate: {
      len: {
        args: [0, 1000],
        msg: 'Note must be less than 1000 characters'
      }
    }
  },
  remind_date: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    validate: {
      isDate: {
        msg: 'Remind date must be a valid date'
      },
      notEmpty: {
        msg: 'Remind date is required'
      }
    }
  },
  is_sent: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  }
}, {
  tableName: 'reminders',
  indexes: [
    {
      fields: ['customer_id']
    },
    {
      fields: ['remind_date']
    },
    {
      fields: ['is_sent']
    },
    {
      fields: ['remind_date', 'is_sent']
    }
  ]
});

module.exports = Reminder;
