const { sequelize } = require('./database');
const { User, Customer, Devi<PERSON>, ServiceRequest, ServiceNote, Invoice, Reminder } = require('../models');
require('dotenv').config();

const seedData = async () => {
  try {
    console.log('Starting database seeding...');

    // Sync database first
    await sequelize.sync({ force: true });
    console.log('Database synced successfully');

    // Create admin user
    const adminUser = await User.create({
      name: process.env.ADMIN_NAME || 'Admin User',
      email: process.env.ADMIN_EMAIL || '<EMAIL>',
      password: process.env.ADMIN_PASSWORD || 'admin123',
      role: 'admin'
    });
    console.log('Admin user created');

    // Create technician user
    const technicianUser = await User.create({
      name: 'John Technician',
      email: '<EMAIL>',
      password: 'tech123456',
      role: 'technician'
    });
    console.log('Technician user created');

    // Create customer service user
    const customerServiceUser = await User.create({
      name: 'Jane Customer Service',
      email: '<EMAIL>',
      password: 'cs123456',
      role: 'customer_service'
    });
    console.log('Customer service user created');

    // Create sample customers
    const customers = await Customer.bulkCreate([
      {
        name: 'Ahmet Yılmaz',
        phone: '+90 ************',
        email: '<EMAIL>',
        address: 'Kadıköy, İstanbul'
      },
      {
        name: 'Fatma Demir',
        phone: '+90 ************',
        email: '<EMAIL>',
        address: 'Beşiktaş, İstanbul'
      },
      {
        name: 'Mehmet Kaya',
        phone: '+90 ************',
        email: '<EMAIL>',
        address: 'Çankaya, Ankara'
      },
      {
        name: 'Ayşe Özkan',
        phone: '+90 ************',
        email: null,
        address: 'Konak, İzmir'
      },
      {
        name: 'Ali Şahin',
        phone: '+90 ************',
        email: '<EMAIL>',
        address: null
      }
    ]);
    console.log('Sample customers created');

    // Create sample devices
    const devices = await Device.bulkCreate([
      {
        customer_id: customers[0].id,
        brand: 'Samsung',
        model: 'Galaxy S21',
        serial_number: 'SM-G991B-001',
        warranty_expiry: '2024-12-31'
      },
      {
        customer_id: customers[0].id,
        brand: 'Apple',
        model: 'MacBook Pro',
        serial_number: 'MBP-2023-001',
        warranty_expiry: '2025-06-15'
      },
      {
        customer_id: customers[1].id,
        brand: 'HP',
        model: 'Pavilion 15',
        serial_number: 'HP-PAV-002',
        warranty_expiry: '2024-08-20'
      },
      {
        customer_id: customers[2].id,
        brand: 'iPhone',
        model: '14 Pro',
        serial_number: 'IP14P-003',
        warranty_expiry: '2025-01-10'
      },
      {
        customer_id: customers[3].id,
        brand: 'Dell',
        model: 'XPS 13',
        serial_number: 'DELL-XPS-004',
        warranty_expiry: null
      },
      {
        customer_id: customers[4].id,
        brand: 'Lenovo',
        model: 'ThinkPad X1',
        serial_number: 'LEN-TP-005',
        warranty_expiry: '2024-11-30'
      }
    ]);
    console.log('Sample devices created');

    // Create sample service requests
    const serviceRequests = await ServiceRequest.bulkCreate([
      {
        device_id: devices[0].id,
        status: 'Pending',
        description: 'Ekran çatlamış, değişim gerekiyor',
        assigned_to: null,
        request_date: new Date(),
        estimated_completion: null
      },
      {
        device_id: devices[1].id,
        status: 'Assigned',
        description: 'Klavye tuşları çalışmıyor',
        assigned_to: 'John Technician',
        request_date: new Date(Date.now() - 86400000), // Yesterday
        estimated_completion: new Date(Date.now() + 172800000) // 2 days from now
      },
      {
        device_id: devices[2].id,
        status: 'InRepair',
        description: 'Batarya şişmiş, değişim gerekiyor',
        assigned_to: 'John Technician',
        request_date: new Date(Date.now() - 172800000), // 2 days ago
        estimated_completion: new Date(Date.now() + 86400000) // Tomorrow
      },
      {
        device_id: devices[3].id,
        status: 'Completed',
        description: 'Su hasarı, anakart temizlendi',
        assigned_to: 'John Technician',
        request_date: new Date(Date.now() - 604800000), // 1 week ago
        estimated_completion: new Date(Date.now() - 86400000) // Yesterday
      },
      {
        device_id: devices[4].id,
        status: 'OnTheWay',
        description: 'Açılmıyor, güç sorunu',
        assigned_to: 'John Technician',
        request_date: new Date(),
        estimated_completion: new Date(Date.now() + 259200000) // 3 days from now
      }
    ]);
    console.log('Sample service requests created');

    // Create sample service notes
    await ServiceNote.bulkCreate([
      {
        service_request_id: serviceRequests[1].id,
        note: 'Cihaz incelendi, klavye değişimi gerekiyor',
        photo: null,
        created_by: 'John Technician'
      },
      {
        service_request_id: serviceRequests[2].id,
        note: 'Batarya çıkarıldı, yeni batarya sipariş edildi',
        photo: null,
        created_by: 'John Technician'
      },
      {
        service_request_id: serviceRequests[3].id,
        note: 'Anakart temizlendi, test edildi, çalışıyor',
        photo: null,
        created_by: 'John Technician'
      },
      {
        service_request_id: serviceRequests[4].id,
        note: 'Müşteri adresine gidiliyor',
        photo: null,
        created_by: 'John Technician'
      }
    ]);
    console.log('Sample service notes created');

    // Create sample invoices
    await Invoice.bulkCreate([
      {
        service_request_id: serviceRequests[3].id,
        amount: 250.00,
        paid: true,
        issue_date: new Date(Date.now() - 86400000), // Yesterday
        due_date: new Date(Date.now() + 1209600000), // 2 weeks from yesterday
        invoice_pdf: null
      },
      {
        service_request_id: serviceRequests[2].id,
        amount: 180.00,
        paid: false,
        issue_date: new Date(),
        due_date: new Date(Date.now() + 1209600000), // 2 weeks from now
        invoice_pdf: null
      }
    ]);
    console.log('Sample invoices created');

    // Create sample reminders
    await Reminder.bulkCreate([
      {
        customer_id: customers[0].id,
        title: 'Garanti süresi bitiyor',
        note: 'Samsung Galaxy S21 garantisi 31 Aralık\'ta bitiyor',
        remind_date: '2024-12-15',
        is_sent: false
      },
      {
        customer_id: customers[1].id,
        title: 'Periyodik bakım zamanı',
        note: 'HP Pavilion için 6 aylık bakım zamanı geldi',
        remind_date: new Date(Date.now() + 86400000).toISOString().split('T')[0], // Tomorrow
        is_sent: false
      },
      {
        customer_id: customers[2].id,
        title: 'Ödeme hatırlatması',
        note: 'iPhone onarım ücreti ödenmedi',
        remind_date: new Date().toISOString().split('T')[0], // Today
        is_sent: false
      }
    ]);
    console.log('Sample reminders created');

    console.log('\n=== SEEDING COMPLETED SUCCESSFULLY ===');
    console.log('\nDefault Users Created:');
    console.log('Admin: <EMAIL> / admin123');
    console.log('Technician: <EMAIL> / tech123');
    console.log('Customer Service: <EMAIL> / cs123');
    console.log('\nSample Data:');
    console.log(`- ${customers.length} customers`);
    console.log(`- ${devices.length} devices`);
    console.log(`- ${serviceRequests.length} service requests`);
    console.log('- 4 service notes');
    console.log('- 2 invoices');
    console.log('- 3 reminders');

  } catch (error) {
    console.error('Seeding failed:', error);
    process.exit(1);
  }
};

// Run seeding if this file is executed directly
if (require.main === module) {
  seedData().then(() => {
    console.log('\nSeeding completed. Exiting...');
    process.exit(0);
  });
}

module.exports = seedData;
